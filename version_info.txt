# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 1, 6, 0),
    prodvers=(1, 1, 6, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'FileDescription', u'PACE - Process Automation for Client Engagements'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'PACE'),
        StringStruct(u'OriginalFilename', u'PACE.exe'),
        StringStruct(u'ProductName', u'PACE'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
