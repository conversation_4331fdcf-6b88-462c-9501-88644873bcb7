"""
Constants for PACE application.
Centralizes all magic numbers and configuration values.
"""

# Application Information
APP_NAME = "PACE"
APP_VERSION = "1.1.4"
APP_FULL_NAME = "Process Automation for Client Engagements"
DEVELOPER_NAME = "Chad Polliard"

# UI Dimensions and Spacing
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600
WINDOW_DEFAULT_WIDTH = 1000
WINDOW_DEFAULT_HEIGHT = 700

FORM_SPACING = 20
BUTTON_SPACING = 10
SECTION_SPACING = 30
MARGIN_STANDARD = 15

# Font Sizes
FONT_SIZE_TITLE = 16
FONT_SIZE_SUBTITLE = 14
FONT_SIZE_NORMAL = 12
FONT_SIZE_SMALL = 10

# Document Generation
DOCUMENT_FONT_SIZE = 12  # 12pt for legal documents
MAX_LINE_LENGTH = 120    # Maximum characters per line
TEMPLATE_TIMEOUT = 30    # Seconds to wait for template processing

# File Sizes (in bytes)
MAX_TEMPLATE_SIZE = 10 * 1024 * 1024  # 10 MB
MAX_OUTPUT_SIZE = 50 * 1024 * 1024    # 50 MB
LARGE_FILE_THRESHOLD = 50 * 1024      # 50 KB

# Pricing Constants
DEFAULT_HOURLY_RATE = 350
MIN_HOURLY_RATE = 200
MAX_HOURLY_RATE = 1000
STANDARD_HOURS_PER_DAY = 8
STANDARD_DAYS_PER_WEEK = 5

# Endpoint Limits
MIN_ENDPOINTS = 1
MAX_ENDPOINTS = 100000
DEFAULT_ENDPOINTS = 100

# File Extensions
TEMPLATE_EXTENSION = ".docx"
OUTPUT_EXTENSION = ".docx"
ICON_EXTENSION = ".ico"

# Directory Names
TEMPLATES_DIR = "templates"
DATA_DIR = "data"
RESOURCES_DIR = "resources"
OUTPUT_DIR = "output"
DIST_DIR = "dist"
BUILD_DIR = "build"

# Template Categories
TEMPLATE_CATEGORIES = {
    "MSA": "msa_templates",
    "SOW": "sow_templates", 
    "BAA": "baa_templates",
    "DPA": "dpa_templates"
}

# Engagement Types
ENGAGEMENT_TYPES = {
    "DFIR": "Digital Forensics and Incident Response",
    "TACI": "Threat Actor and Compromise Investigation", 
    "BEC": "Business Email Compromise",
    "RR": "Ransomware Recovery"
}

# Status Messages
STATUS_READY = "Ready"
STATUS_GENERATING = "Generating documents..."
STATUS_COMPLETE = "Documents generated successfully"
STATUS_ERROR = "Error occurred"

# Progress Dialog
PROGRESS_MIN = 0
PROGRESS_MAX = 100
PROGRESS_STEP = 10

# Logging Levels
LOG_LEVEL_DEBUG = "DEBUG"
LOG_LEVEL_INFO = "INFO"
LOG_LEVEL_WARNING = "WARNING"
LOG_LEVEL_ERROR = "ERROR"

# Build Configuration
PYINSTALLER_TIMEOUT = 300  # 5 minutes
BUILD_RETRY_COUNT = 3
ICON_SIZES = [16, 32, 48, 64, 128, 256]

# Network Timeouts (seconds)
NETWORK_TIMEOUT = 30
DOWNLOAD_TIMEOUT = 60
UPLOAD_TIMEOUT = 120

# Validation Limits
MAX_CLIENT_NAME_LENGTH = 100
MAX_ADDRESS_LENGTH = 200
MAX_EMAIL_LENGTH = 100
MAX_PHONE_LENGTH = 20

# Error Codes
ERROR_FILE_NOT_FOUND = 1001
ERROR_TEMPLATE_INVALID = 1002
ERROR_GENERATION_FAILED = 1003
ERROR_SAVE_FAILED = 1004
ERROR_PERMISSION_DENIED = 1005

# Success Codes
SUCCESS_DOCUMENT_GENERATED = 2001
SUCCESS_TEMPLATE_LOADED = 2002
SUCCESS_FILE_SAVED = 2003

# Color Codes (for styling)
COLOR_PRIMARY = "#2E86AB"
COLOR_SECONDARY = "#A23B72"
COLOR_SUCCESS = "#F18F01"
COLOR_ERROR = "#C73E1D"
COLOR_WARNING = "#F4A261"

# File Patterns
BACKUP_PATTERN = "*_backup_*"
TEMP_PATTERN = "*_temp_*"
LOG_PATTERN = "*.log"

# Date Formats
DATE_FORMAT_DISPLAY = "%B %d, %Y"
DATE_FORMAT_FILE = "%Y%m%d_%H%M%S"
DATE_FORMAT_LOG = "%Y-%m-%d %H:%M:%S"

# Memory Limits (in MB)
MAX_MEMORY_USAGE = 512
WARNING_MEMORY_THRESHOLD = 256

# Performance Thresholds
FAST_OPERATION_MS = 100
SLOW_OPERATION_MS = 5000
VERY_SLOW_OPERATION_MS = 10000
