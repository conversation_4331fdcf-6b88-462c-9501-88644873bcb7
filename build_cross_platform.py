#!/usr/bin/env python3
"""
Cross-platform build script for PACE v1.1.5
Creates standalone executables for Windows and macOS with platform-specific features.
"""

import os
import sys
import shutil
import subprocess
import importlib.util
import platform
from pathlib import Path

# Platform detection
IS_WINDOWS = platform.system() == "Windows"
IS_MACOS = platform.system() == "Darwin"
IS_LINUX = platform.system() == "Linux"

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    pyinstaller_spec = importlib.util.find_spec("PyInstaller")
    if pyinstaller_spec is not None:
        print("✓ PyInstaller already installed")
        return True
    else:
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install PyInstaller: {e}")
            return False

def create_cross_platform_spec():
    """Create PyInstaller spec file with platform-specific configurations"""
    
    # Platform-specific settings
    if IS_WINDOWS:
        app_name = 'PACE'
        icon_file = 'PACE.ico'
        console_mode = False
        version_info = 'version_info.txt'
        bundle_section = ""
    elif IS_MACOS:
        app_name = 'PACE'
        icon_file = 'PACE.icns'  # macOS uses .icns format
        console_mode = False
        version_info = None
        bundle_section = """
app = BUNDLE(
    exe,
    name='PACE.app',
    icon='PACE.icns',
    bundle_identifier='com.yourcompany.pace',
    info_plist={
        'CFBundleName': 'PACE',
        'CFBundleDisplayName': 'PACE - Process Automation for Client Engagements',
        'CFBundleVersion': '1.1.5',
        'CFBundleShortVersionString': '1.1.5',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.13.0',
    },
)"""
    else:  # Linux
        app_name = 'PACE'
        icon_file = 'PACE.png'
        console_mode = False
        version_info = None
        bundle_section = ""

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Define the main script and data files
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('data', 'data'),
        ('resources', 'resources'),
        ('README.md', '.'),
        ('CHANGELOG.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets',
        'PySide6.QtGui',
        'docxtpl',
        'docx',
        'docxcompose',
        'jinja2',
        'lxml',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={console_mode},
    disable_windowed_traceback=False,
    argv_emulation={IS_MACOS},
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{icon_file}',{f"""
    version='{version_info}'""" if version_info else ""}
)
{bundle_section}
'''
    
    with open('PACE_cross_platform.spec', 'w') as f:
        f.write(spec_content)
    print(f"✓ Created cross-platform PACE_cross_platform.spec file for {platform.system()}")

def check_icon_file():
    """Check for appropriate icon file based on platform"""
    if IS_WINDOWS:
        icon_file = 'PACE.ico'
    elif IS_MACOS:
        icon_file = 'PACE.icns'
    else:
        icon_file = 'PACE.png'
    
    if not os.path.exists(icon_file):
        print(f"❌ Error: {icon_file} not found!")
        print(f"   Please place your {icon_file} file in the root directory before building.")
        
        # Suggest conversion if Windows icon exists but macOS icon doesn't
        if IS_MACOS and os.path.exists('PACE.ico'):
            print("   You can convert PACE.ico to PACE.icns using:")
            print("   - Online converters like cloudconvert.com")
            print("   - macOS built-in tools: sips -s format icns PACE.ico --out PACE.icns")
        
        return False
    else:
        print(f"✓ {icon_file} found")
        return True

def create_version_info():
    """Create version information file for Windows executable"""
    if not IS_WINDOWS:
        return  # Only needed for Windows
        
    version_info = '''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 1, 6, 0),
    prodvers=(1, 1, 6, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'FileDescription', u'PACE - Process Automation for Client Engagements'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'PACE'),
        StringStruct(u'OriginalFilename', u'PACE.exe'),
        StringStruct(u'ProductName', u'PACE'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w') as f:
        f.write(version_info)
    print("✓ Created version_info.txt file")

def create_macos_dmg():
    """Create a DMG installer for macOS"""
    if not IS_MACOS:
        return True  # Skip on non-macOS platforms

    print("Creating macOS DMG installer...")
    try:
        dmg_name = "PACE_v1.1.5_macOS.dmg"
        app_path = "dist/PACE.app"

        if not os.path.exists(app_path):
            print("✗ PACE.app not found, cannot create DMG")
            return False

        # Create DMG using hdiutil
        cmd = [
            "hdiutil", "create", "-volname", "PACE v1.1.5",
            "-srcfolder", "dist", "-ov", "-format", "UDZO",
            dmg_name
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Created {dmg_name}")
            return True
        else:
            print(f"✗ Failed to create DMG: {result.stderr}")
            return False

    except Exception as e:
        print(f"✗ DMG creation error: {e}")
        return False

def build_executable():
    """Build the standalone executable"""
    print(f"Building PACE executable for {platform.system()}...")

    # Check icon file
    if not check_icon_file():
        return False

    try:
        # Clean previous builds
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')

        # Build using spec file
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "PACE_cross_platform.spec"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✓ Executable built successfully!")

            # Check if executable was created (platform-specific paths)
            if IS_WINDOWS:
                exe_path = Path("dist/PACE.exe")
            elif IS_MACOS:
                exe_path = Path("dist/PACE.app")
            else:
                exe_path = Path("dist/PACE")

            if exe_path.exists():
                if IS_MACOS:
                    # For macOS app bundles, show the .app size
                    try:
                        result = subprocess.run(['du', '-sh', str(exe_path)], capture_output=True, text=True)
                        size_info = result.stdout.split()[0] if result.returncode == 0 else "unknown"
                        print(f"✓ PACE.app created: {size_info}")
                    except:
                        print("✓ PACE.app created")
                else:
                    size_mb = exe_path.stat().st_size / (1024 * 1024)
                    print(f"✓ {exe_path.name} created: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executable not found in dist folder")
                return False
        else:
            print("✗ Build failed:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_license_file():
    """Create a simple license file"""
    license_text = """PACE - Process Automation for Client Engagements
Version 1.1.5

This software is for internal business use only.
All rights reserved.

Copyright (C) 2025 Your Company Name

This software is provided "as is" without warranty of any kind.
"""

    with open('LICENSE.txt', 'w') as f:
        f.write(license_text)
    print("✓ Created LICENSE.txt file")

def main():
    """Main build process"""
    print(f"PACE v1.1.5 - Cross-Platform Build Script")
    print(f"Building for: {platform.system()} ({platform.machine()})")
    print("=" * 50)

    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("✗ Error: main.py not found. Run this script from the PACE root directory.")
        return False

    # Install PyInstaller
    if not install_pyinstaller():
        return False

    # Create build files
    create_cross_platform_spec()
    create_version_info()  # Only creates on Windows
    create_license_file()

    # Build executable
    if not build_executable():
        return False

    # Create platform-specific installer
    if IS_MACOS:
        create_macos_dmg()
    elif IS_WINDOWS:
        print("\nNote: For Windows installer, use the existing build_executable.py script")
        print("which includes Inno Setup configuration.")

    print("\n" + "=" * 50)
    print("✓ BUILD COMPLETE!")
    print("=" * 50)

    if IS_WINDOWS:
        print(f"Executable: dist/PACE.exe")
        print("For installer: Use build_executable.py script")
    elif IS_MACOS:
        print(f"Application: dist/PACE.app")
        print(f"Installer: PACE_v1.1.5_macOS.dmg")
        print("\nNext steps:")
        print("1. Test the application: open dist/PACE.app")
        print("2. Distribute the DMG file for easy installation")
    else:
        print(f"Executable: dist/PACE")

    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
